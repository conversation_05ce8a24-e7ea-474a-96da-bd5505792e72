
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>edin, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";

export const Hero = () => {
  const scrollToAbout = () => {
    document.querySelector("#about")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Enhanced Background with Multiple Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-blue-500/5 to-purple-500/10"></div>
      
      {/* Animated Background Elements */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-tl from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: "2s" }}></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-teal-400/10 to-blue-400/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: "1s" }}></div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="max-w-5xl mx-auto text-center">
          {/* Enhanced Profile Picture */}
          <div className="mb-8 animate-fade-in">
            <div className="relative w-40 h-40 mx-auto">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 rounded-full animate-spin" style={{ animationDuration: "8s" }}></div>
              <div className="absolute inset-2 bg-white rounded-full flex items-center justify-center shadow-xl">
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <Code size={40} className="text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Refined Typography */}
          <div className="animate-fade-in space-y-4" style={{ animationDelay: "0.2s" }}>
            <div className="space-y-2">
              <h1 className="text-4xl md:text-6xl font-semibold text-slate-900 mb-4 leading-tight tracking-tight">
                Hi, I'm{" "}
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent font-medium">
                  Devansh
                </span>
              </h1>
              <div className="h-0.5 w-24 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
            </div>
          </div>

          {/* Refined Description */}
          <div className="animate-fade-in" style={{ animationDelay: "0.4s" }}>
            <p className="text-lg md:text-xl text-slate-600 mb-8 leading-relaxed font-normal max-w-3xl mx-auto">
              A passionate{" "}
              <span className="font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Full-Stack Developer
              </span>{" "}
              crafting exceptional digital experiences and exploring the limitless possibilities of{" "}
              <span className="font-medium text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-teal-600">
                AI & Machine Learning
              </span>
            </p>
          </div>

          {/* Refined Tags */}
          <div className="animate-fade-in flex flex-wrap justify-center gap-4 mb-12" style={{ animationDelay: "0.6s" }}>
            <div className="group flex items-center gap-2 px-5 py-2.5 bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-white/30 hover:shadow-lg transition-all duration-300 hover:scale-105">
              <Sparkles className="text-blue-600 group-hover:animate-spin" size={18} />
              <span className="text-sm font-medium text-slate-700">Innovation Driven</span>
            </div>
            <div className="group flex items-center gap-2 px-5 py-2.5 bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-white/30 hover:shadow-lg transition-all duration-300 hover:scale-105">
              <Code className="text-purple-600 group-hover:scale-110 transition-transform" size={18} />
              <span className="text-sm font-medium text-slate-700">Problem Solver</span>
            </div>
          </div>

          {/* Refined CTA Section */}
          <div className="animate-fade-in space-y-6" style={{ animationDelay: "0.8s" }}>
            <Button
              onClick={scrollToAbout}
              size="lg"
              className="group bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 hover:from-blue-700 hover:via-purple-700 hover:to-teal-700 text-white px-8 py-3 rounded-full shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 text-base font-medium"
            >
              Discover My Journey
              <ArrowDown className="ml-2 group-hover:animate-bounce" size={18} />
            </Button>

            {/* Social Links */}
            <div className="flex justify-center space-x-4">
              <a 
                href="https://github.com/devansh1234523" 
                target="_blank"
                rel="noopener noreferrer"
                className="group p-3 bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-white/30 hover:shadow-lg transition-all duration-300 hover:scale-110"
              >
                <Github className="text-slate-600 group-hover:text-blue-600 transition-colors" size={20} />
              </a>
              <a 
                href="https://www.linkedin.com/in/devansh-prajapati-180b3a285/" 
                target="_blank"
                rel="noopener noreferrer"
                className="group p-3 bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-white/30 hover:shadow-lg transition-all duration-300 hover:scale-110"
              >
                <Linkedin className="text-slate-600 group-hover:text-blue-600 transition-colors" size={20} />
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="group p-3 bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-white/30 hover:shadow-lg transition-all duration-300 hover:scale-110"
              >
                <Mail className="text-slate-600 group-hover:text-purple-600 transition-colors" size={20} />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
