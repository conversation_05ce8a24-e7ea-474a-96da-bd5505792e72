
import { <PERSON>Link, Gith<PERSON>, DollarSign, Heart } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export const Projects = () => {
  const projects = [
    {
      title: "Budget Visual Companion",
      description: "A comprehensive personal expense and budget management application featuring user authentication, expense tracking, categorized dashboards, and responsive UI design.",
      icon: <DollarSign className="text-green-600" size={28} />,
      features: ["User Authentication", "Expense Tracking", "Visual Dashboards", "Category Management", "Responsive Design"],
      technologies: ["React", "Node.js", "MongoDB", "Chart.js", "Tailwind CSS"],
      color: "from-green-500/10 to-blue-500/10"
    },
    {
      title: "FoodShare Connect",
      description: "A community-driven platform for food donation connecting donors with those in need, featuring geolocation-based matching and real-time request handling.",
      icon: <Heart className="text-red-600" size={28} />,
      features: ["Donation Posting", "Request Handling", "Geolocation Matching", "Community Platform", "Real-time Updates"],
      technologies: ["React", "Express", "PostgreSQL", "Socket.io", "Google Maps API"],
      color: "from-red-500/10 to-orange-500/10"
    }
  ];

  return (
    <section id="projects" className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-semibold text-slate-800 mb-6">
              Featured Projects
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto font-normal">
              Here are some of the projects I've worked on, showcasing my skills in
              full-stack development and problem-solving.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <Card key={index} className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm hover:scale-105">
                <CardHeader className={`bg-gradient-to-br ${project.color} rounded-t-lg`}>
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-white rounded-xl shadow-sm">
                      {project.icon}
                    </div>
                    <CardTitle className="text-xl font-medium text-slate-800">
                      {project.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-slate-600 mb-6 leading-relaxed font-normal">
                    {project.description}
                  </p>

                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-slate-700 mb-3">Key Features</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.features.map((feature, featureIndex) => (
                        <span
                          key={featureIndex}
                          className="px-3 py-1.5 bg-blue-50 text-blue-700 text-xs rounded-full font-normal"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-slate-700 mb-3">Technologies Used</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-3 py-1.5 bg-slate-100 text-slate-700 text-xs rounded-full font-normal"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <Button variant="outline" size="sm" className="flex items-center gap-2 font-normal">
                      <Github size={16} />
                      Code
                    </Button>
                    <Button size="sm" className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 font-normal">
                      <ExternalLink size={16} />
                      Live Demo
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
