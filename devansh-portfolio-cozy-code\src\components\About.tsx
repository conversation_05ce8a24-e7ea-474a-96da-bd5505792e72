
import { <PERSON>, <PERSON>, Zap, Target } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export const About = () => {
  const skills = [
    { category: "Frontend", items: ["React", "Next.js", "TypeScript", "Tailwind CSS"] },
    { category: "Backend", items: ["Node.js", "Python", "Express", "FastAPI"] },
    { category: "Database", items: ["MongoDB", "PostgreSQL", "Firebase"] },
    { category: "AI/ML", items: ["TensorFlow", "PyTorch", "Scikit-learn", "Pandas"] },
  ];

  const values = [
    {
      icon: <Brain className="text-blue-600" size={24} />,
      title: "Continuous Learning",
      description: "Always exploring new technologies and methodologies to stay at the forefront of innovation."
    },
    {
      icon: <Users className="text-purple-600" size={24} />,
      title: "Collaboration",
      description: "Believing in the power of teamwork to create solutions that exceed individual capabilities."
    },
    {
      icon: <Zap className="text-teal-600" size={24} />,
      title: "Innovation",
      description: "Driven by curiosity to find creative solutions to complex problems."
    },
    {
      icon: <Target className="text-orange-600" size={24} />,
      title: "Impact Focus",
      description: "Committed to building technology that makes a meaningful difference."
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-white to-slate-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-semibold text-slate-800 mb-6">
              About Me
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed font-normal">
              I'm a dedicated software developer with a passion for creating scalable applications
              and diving deep into AI and machine learning technologies. My journey is fueled by
              curiosity, innovation, and a collaborative spirit.
            </p>
          </div>

          {/* Values */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {values.map((value, index) => (
              <Card key={index} className="hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-6 text-center">
                  <div className="mb-4 flex justify-center">
                    <div className="p-3 bg-slate-50 rounded-full">
                      {value.icon}
                    </div>
                  </div>
                  <h3 className="text-lg font-medium text-slate-800 mb-2">
                    {value.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed font-normal">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Skills */}
          <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-medium text-slate-800 mb-8 text-center">
              Technical Skills
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {skills.map((skillGroup, index) => (
                <div key={index}>
                  <h4 className="text-lg font-medium text-slate-700 mb-4">
                    {skillGroup.category}
                  </h4>
                  <div className="space-y-2">
                    {skillGroup.items.map((skill, skillIndex) => (
                      <div
                        key={skillIndex}
                        className="bg-white px-4 py-2.5 rounded-lg text-sm text-slate-600 shadow-sm hover:shadow-md transition-shadow duration-200 font-normal"
                      >
                        {skill}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
