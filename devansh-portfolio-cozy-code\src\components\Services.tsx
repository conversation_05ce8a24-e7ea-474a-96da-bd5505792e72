
import { Code2, Palette, Smartphone, Database } from "lucide-react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";

export const Services = () => {
  const services = [
    {
      icon: <Code2 className="text-blue-600" size={28} />,
      title: "Software Development",
      description: "Custom software solutions built with modern technologies, focusing on scalability, performance, and maintainability.",
      features: ["Full-Stack Applications", "API Development", "Database Design", "Performance Optimization"]
    },
    {
      icon: <Palette className="text-purple-600" size={28} />,
      title: "Web Design",
      description: "Modern, responsive web designs that combine aesthetic appeal with exceptional user experience and functionality.",
      features: ["Responsive Design", "UI/UX Optimization", "Brand Integration", "Performance Focus"]
    }
  ];

  return (
    <section id="services" className="py-20 bg-gradient-to-br from-white to-slate-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-semibold text-slate-800 mb-6">
              Services
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto font-normal">
              I offer comprehensive development services to help bring your ideas to life
              with modern, scalable, and user-friendly solutions.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {services.map((service, index) => (
              <Card key={index} className="hover:shadow-xl transition-all duration-500 border-0 bg-gradient-to-br from-slate-50 to-blue-50 hover:scale-105">
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-4 p-4 bg-white rounded-xl shadow-lg w-fit">
                    {service.icon}
                  </div>
                  <CardTitle className="text-xl font-medium text-slate-800">
                    {service.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-slate-600 mb-6 leading-relaxed font-normal">
                    {service.description}
                  </p>
                  <div className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center justify-center gap-2">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                        <span className="text-sm text-slate-600 font-normal">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Additional Skills Highlight */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center shadow-xl">
            <h3 className="text-2xl font-medium mb-4">
              Ready to Build Something Amazing?
            </h3>
            <p className="text-lg opacity-90 mb-6 font-normal">
              Let's collaborate to create innovative solutions that make a real impact.
            </p>
            <div className="flex flex-wrap justify-center gap-6">
              <div className="flex items-center gap-2">
                <Smartphone size={20} />
                <span className="font-normal">Mobile-First</span>
              </div>
              <div className="flex items-center gap-2">
                <Database size={20} />
                <span className="font-normal">Scalable Architecture</span>
              </div>
              <div className="flex items-center gap-2">
                <Code2 size={20} />
                <span className="font-normal">Clean Code</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
